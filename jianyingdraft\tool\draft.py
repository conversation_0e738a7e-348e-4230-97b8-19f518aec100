# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/10 下午4:46
File Name:draft.py
"""
from jianyingdraft.tool.export import ExportDraft
import uuid
import json
import os
from jianyingdraft.config import SAVE_PATH
from jianyingdraft.utils.response import ToolResponse
from jianyingdraft.server import mcp


class Draft:
    def __init__(self):
        pass

    @mcp.tool()
    def create_draft(self, draft_name: str = '', width: int = 1920, height: int = 1080, fps: int = 30):
        """
        创建草稿

        Args:
            draft_name:  str 草稿名称，默认为空，不过最好加上
            width: int,视频宽度,默认1920
            height: int，视频高度，默认1080
            fps: int，帧率，默认30
        """
        # 验证SAVE_PATH是否存在
        if not os.path.exists(SAVE_PATH):
            return ToolResponse(success=False, message=f"草稿存储路径不存在: {SAVE_PATH}").__dict__
        # 生成草稿ID
        draft_id = str(uuid.uuid4())
        # 构建完整的草稿路径
        draft_path = os.path.join(SAVE_PATH, draft_id)

        # 创建草稿数据
        draft_data = {
            "draft_id": draft_id,
            "draft_name": draft_name,
            "width": width,
            "height": height,
            "fps": fps
        }
        # 在SAVE_PATH下创建以草稿ID命名的文件夹
        os.makedirs(draft_path, exist_ok=True)

        # 保存draft.json文件
        draft_json_path = os.path.join(draft_path, "draft.json")
        with open(draft_json_path, "w", encoding="utf-8") as f:
            json.dump(draft_data, f, ensure_ascii=False, indent=4)
        response = ToolResponse(success=True, message="草稿创建成功", data=draft_data)
        return response.__dict__

    def export_draft(self, draft_id: str, output_path: str = ''):
        """
        导出草稿

        Args:
            draft_id: str, 草稿ID
            output_path: str, 导出路径,可选，默认"./output"

        Returns:
            str: 导出结果信息
        """
        try:
            export_manager = ExportDraft(output_path)
            data = export_manager.export(draft_id)
            return ToolResponse(success=True, message="导出成功", data=data).__dict__
        except Exception as e:
            return ToolResponse(success=False, message=f"导出失败: {e}").__dict__


if __name__ == '__main__':
    try:
        draft = Draft()
        result = draft.create_draft(draft_name='测试草稿')
        print(f"草稿创建成功: {result['draft_id']}")
    except FileNotFoundError as e:
        print(f"路径错误: {e}")
    except FileExistsError as e:
        print(f"草稿已存在: {e}")
    except Exception as e:
        print(f"创建失败: {e}")
