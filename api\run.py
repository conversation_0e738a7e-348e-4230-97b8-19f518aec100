# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON>i
<PERSON>reate Time: 2025/8/9 下午11:01
File Name:test_run.py
"""
from api.api_main import app
import uvicorn

def main():
    """
    直接运行FastAPI应用，支持在IDE中设置断点进行调试
    """
    print("启动API服务，可以在IDE中设置断点进行调试")
    print("API文档地址: http://127.0.0.1:8000/docs")

    # 使用uvicorn直接运行应用
    uvicorn.run(
        app,
        host="127.0.0.1",
        port=8000,
        # 生产环境下建议设置为False
        reload=False,
    )


if __name__ == "__main__":
    main()