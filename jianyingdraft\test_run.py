# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>
Create Time: 2025/8/12 上午10:44
File Name:test_run.py
"""
from jianyingdraft.tool.draft import Draft
from jianyingdraft.tool.track import Track
from jianyingdraft.tool.audio import AudioSegment
from jianyingdraft.tool.text import TextSegment
from jianyingdraft.tool.export import ExportDraft
from jianyingdraft.tool.video import VideoSegment

# 创建草稿
draft = Draft()
draft_id = draft.create_draft(draft_name='3')['draft_id']
# 创建轨道
text_track_id = Track(draft_id).add_track(track_type='text', track_name='text')
video_track_id = Track(draft_id).add_track(track_type='video', track_name='video')
Track(draft_id).add_track(track_type='audio', track_name='audio')
# 创建音频片段
audio_segment = AudioSegment(draft_id, track_name='audio')
audio_segment.add_audio_segment(material=r'D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\audio.mp3',
                                target_timerange='0s-5s')
audio_segment.add_fade('1s', '0.5s')
audio_segment.add_keyframe('0.5s', 0.5)
audio_segment.add_effect('AudioSceneEffectType', '电音')

# 创建文本片段

text_segment = TextSegment(
    draft_id=draft_id,
    track_name="text"
)
add_text_segment_params = text_segment.add_text_segment(
    text="示例文本",
    timerange="0s-5s",
    font="Arial",
    style={
        "size": 20.0,
        "bold": False,
        "italic": False,
        "underline": True,
        "color": (1.0, 0.0, 0.0),  # 红色
        "alpha": 0.8,
        "align": 0,  # 居中对齐
        "vertical": False,
        "letter_spacing": 2,
        "line_spacing": 1.5,
        "auto_wrapping": True,
        "max_line_width": 0.9
    },
    clip_settings={
        "alpha": 0.9,
        "flip_horizontal": True,
        "flip_vertical": True,
        "rotation": 15.0,
        "scale_x": 1.2,
        "scale_y": 1.1,
        "transform_x": 0.1,
        "transform_y": -0.1
    },
    border={
        "alpha": 0.7,
        "color": (0.0, 0.0, 1.0),  # 蓝色
        "width": 50.0
    },
    background={
        "color": "#FFFF00",  # 黄色
        "style": 2,
        "alpha": 0.6,
        "round_radius": 0.1,
        "height": 0.2,
        "width": 0.3,
        "horizontal_offset": 0.4,
        "vertical_offset": 0.6
    }
)
text_segment.add_animation('TextIntro', animation_name='向上滑动', duration='1s')
text_segment.add_animation('TextOutro', animation_name='右上弹出', duration='1s')
video_segment = VideoSegment(draft_id, track_name='video')
video_segment.add_video_segment(
    material=r'D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4',
    target_timerange='0s-5s',
    speed=0.8,
    change_pitch=True
)
video_segment.add_animation('IntroType', animation_name='上下抖动', duration='1s')
# video_segment.add_animation('OutroType', animation_name='向上滑动',duration='1s')
video_segment.add_transition('叠化', '1s')
video_segment.add_keyframe('position_x', '3s', 0.5)
video_segment.add_filter('冬漫', intensity=50.0)
video_segment2 = VideoSegment(draft_id, track_name='video')
video_segment2.add_video_segment(
    material=r'D:\pythonProject\MyProject\pyJianYingDraft\readme_assets\tutorial\video.mp4',
    target_timerange='5s-5s'
)
video_segment2.add_background_filling('blur', blur=0.5)
video_segment2.add_mask(
    mask_type='爱心',
    center_x=0.5,
    center_y=0.5,
    size=0.5,
    rotation=0.0,
    feather=0.0,
    invert=False,
    rect_width=0.5,
    round_corner=0.0
)

ExportDraft().export(draft_id)
