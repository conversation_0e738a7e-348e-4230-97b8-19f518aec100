# Introduction

> Get started with the Model Context Protocol (MCP)

MCP is an open protocol that standardizes how applications provide context to large language models (LLMs). Think of MCP like a USB-C port for AI applications. Just as USB-C provides a standardized way to connect your devices to various peripherals and accessories, MCP provides a standardized way to connect AI models to different data sources and tools. MCP enables you build agents and complex workflows on top of LLMs and connects your models with the world.

MCP provides:

* **A growing list of pre-built integrations** that your LLM can directly plug into
* **A standardized way** to build custom integrations for AI applications
* **An open protocol** that everyone is free to implement and use
* **The flexibility to change** between different apps and take your context with you

## Choose Your Path

<CardGroup cols={2}>
  <Card title="Understand Concepts" icon="book" href="/docs/learn/architecture">
    Learn the core concepts and architecture of MCP
  </Card>

  {" "}

  <Card title="Use MCP" icon="plug" href="/docs/tutorials/use-remote-mcp-server">
    Connect to existing MCP servers and start using them
  </Card>

  {" "}

  <Card title="Build Servers" icon="server" href="/quickstart/server">
    Create MCP servers to expose your data and tools
  </Card>

  <Card title="Build Clients" icon="computer" href="/quickstart/client">
    Develop applications that connect to MCP servers
  </Card>
</CardGroup>

## Ready to Build?

MCP provides official **SDKs** in multiple languages, see the [SDK documentation](/docs/sdk) to find the right SDK for your project. The SDKs handle the protocol details so you can focus on building your features.
