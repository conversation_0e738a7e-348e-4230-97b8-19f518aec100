# 作者：Esther
# FastAPI主入口，只做路由聚合和健康检查
from fastapi import FastAPI

from .draft_script import router as draft_router
from .effect_api import router as effect_router

app = FastAPI(
    title="pyJianYingDraft API服务",
    description="剪映草稿自动化API接口",
    version="1.0.0"
)


@app.get("/")
async def root():
    """
    健康检查接口
    """
    return {"msg": "API服务已启动，欢迎使用, 剪映自动化生成!"}


# 注册草稿管理模块路由
app.include_router(draft_router)

# 注册特效管理模块路由
app.include_router(effect_router)
