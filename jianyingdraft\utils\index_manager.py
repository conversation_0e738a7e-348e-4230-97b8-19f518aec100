# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/13
File Name: index_manager.py
索引管理器 - 管理全局ID到草稿ID的映射关系
"""
import json
import os
from typing import Optional, Dict, Any
from jianyingdraft.config import SAVE_PATH


class IndexManager:
    """索引管理器，用于维护ID到草稿ID的映射关系"""
    
    def __init__(self):
        self.index_file = os.path.join(SAVE_PATH, "index.json")
        self._ensure_index_file_exists()
    
    def _ensure_index_file_exists(self):
        """确保索引文件存在"""
        if not os.path.exists(self.index_file):
            os.makedirs(os.path.dirname(self.index_file), exist_ok=True)
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump({}, f)
    
    def _load_index(self) -> Dict[str, Dict[str, str]]:
        """加载索引数据"""
        try:
            with open(self.index_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            # 如果文件不存在或损坏，返回空字典
            return {}
    
    def _save_index(self, index_data: Dict[str, Dict[str, str]]):
        """保存索引数据"""
        try:
            with open(self.index_file, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存索引文件失败: {e}")
    
    def add_to_index(self, id_value: str, draft_id: str, id_type: str):
        """
        添加ID到索引
        
        Args:
            id_value: 要添加的ID（轨道ID、片段ID等）
            draft_id: 对应的草稿ID
            id_type: ID类型，支持 "track", "video_segment", "audio_segment", "text_segment"
        """
        index_data = self._load_index()
        index_data[id_value] = {
            "draft_id": draft_id,
            "type": id_type
        }
        self._save_index(index_data)
    
    def remove_from_index(self, id_value: str):
        """
        从索引中移除ID
        
        Args:
            id_value: 要移除的ID
        """
        index_data = self._load_index()
        index_data.pop(id_value, None)
        self._save_index(index_data)
    
    def get_draft_id(self, any_id: str) -> Optional[str]:
        """
        通过任意ID获取对应的草稿ID
        
        Args:
            any_id: 任意ID（轨道ID、片段ID等）
            
        Returns:
            对应的草稿ID，如果不存在返回None
        """
        index_data = self._load_index()
        item = index_data.get(any_id)
        return item["draft_id"] if item else None
    
    def get_id_type(self, any_id: str) -> Optional[str]:
        """
        获取ID的类型
        
        Args:
            any_id: 任意ID
            
        Returns:
            ID类型，如果不存在返回None
        """
        index_data = self._load_index()
        item = index_data.get(any_id)
        return item["type"] if item else None
    
    def get_id_info(self, any_id: str) -> Optional[Dict[str, str]]:
        """
        获取ID的完整信息
        
        Args:
            any_id: 任意ID
            
        Returns:
            包含draft_id和type的字典，如果不存在返回None
        """
        index_data = self._load_index()
        return index_data.get(any_id)
    
    def remove_draft_from_index(self, draft_id: str):
        """
        从索引中移除草稿相关的所有ID
        
        Args:
            draft_id: 草稿ID
        """
        index_data = self._load_index()
        # 找到所有属于该草稿的ID并移除
        ids_to_remove = [
            id_value for id_value, info in index_data.items()
            if info.get("draft_id") == draft_id
        ]
        
        for id_value in ids_to_remove:
            index_data.pop(id_value, None)
        
        self._save_index(index_data)


# 全局索引管理器实例
index_manager = IndexManager()


# 便利函数
def add_to_index(id_value: str, draft_id: str, id_type: str):
    """添加ID到索引的便利函数"""
    index_manager.add_to_index(id_value, draft_id, id_type)


def remove_from_index(id_value: str):
    """从索引中移除ID的便利函数"""
    index_manager.remove_from_index(id_value)


def get_draft_id(any_id: str) -> Optional[str]:
    """获取草稿ID的便利函数"""
    return index_manager.get_draft_id(any_id)


def get_id_type(any_id: str) -> Optional[str]:
    """获取ID类型的便利函数"""
    return index_manager.get_id_type(any_id)


def get_id_info(any_id: str) -> Optional[Dict[str, str]]:
    """获取ID信息的便利函数"""
    return index_manager.get_id_info(any_id)


def remove_draft_from_index(draft_id: str):
    """移除草稿相关所有ID的便利函数"""
    index_manager.remove_draft_from_index(draft_id)
