# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/12 下午6:19
File Name:server.py
"""
import os
import sys
import logging
from pathlib import Path

from mcp.server.fastmcp import FastMCP

mcp = FastMCP("JianYingDraft")
# 将当前目录添加到python项目
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from jianyingdraft.tool.draft_tool import draft_tools
from jianyingdraft.tool.track_tool import track_tools
from jianyingdraft.tool.video_tool import video_tools


def main():
    # 注册所有工具
    draft_tools(mcp)
    track_tools(mcp)
    video_tools(mcp)
    mcp.run()


if __name__ == "__main__":
    main()
