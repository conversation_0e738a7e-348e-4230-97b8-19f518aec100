# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>
Create Time: 2025/8/9 下午11:05
File Name:effect_api.py
"""
import logging
from typing import List, Any, Optional

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from pyJianYingDraft import VideoSceneEffectType, TextIntro, TextOutro, TextLoopAnim, IntroType, OutroType, \
    GroupAnimationType, VideoCharacterEffectType
from pyJianYingDraft.metadata.audio_effect_meta import ToneEffectType, AudioSceneEffectType, SpeechToSongType
from pyJianYingDraft.metadata.filter_meta import FilterType
from pyJianYingDraft.metadata.mask_meta import MaskType
from pyJianYingDraft.metadata.transition_meta import TransitionType
from pyJianYingDraft.metadata.font_meta import FontType

# 配置日志记录器
logger = logging.getLogger(__name__)


# ================== 数据模型 ==================
class EffectData(BaseModel):
    """
    特效数据模型

    Attributes:
        name: 效果名称
        resource_id: 资源ID
        is_vip: 是否为VIP特权
    """
    name: str = None
    """效果名称"""
    resource_id: str = None
    """资源ID"""
    is_vip: bool = None
    """是否为VIP特权"""


class EffectResponse(BaseModel):
    """
    特效响应模型

    Attributes:
        code: 响应状态码
        message: 响应消息
        data: 特效数据，可选
    """
    code: int
    message: str
    data: Optional[Any] = None


# ================== API接口层 ==================

# 创建特效模块的路由器
router = APIRouter(prefix="/api/v1/effects", tags=["特效管理"])

# 特效类型映射 - 共15个特效类型
EFFECT_TYPE_MAPPING = {
    "video_scene": VideoSceneEffectType,  # 1. 画面特效类型
    "tone": ToneEffectType,  # 2. 音频音色
    "audio_scene": AudioSceneEffectType,  # 3. 音频场景
    "filter": FilterType,  # 4. 滤镜
    "speech_to_song": SpeechToSongType,  # 5. 语音转歌曲
    "mask": MaskType,  # 6. 蒙版
    "transition": TransitionType,  # 7. 转场
    "font": FontType,  # 8. 字体
    "text_intro": TextIntro,  # 9. 文字入场
    "text_outro": TextOutro,  # 10. 文字出场
    "text_loop_anim": TextLoopAnim,  # 11. 文字循环动画
    "group_animation": GroupAnimationType,  # 12. 组合动画
    "video_character": VideoCharacterEffectType,  # 13. 视频人物特效
    "intro": IntroType,  # 14. 视频/图片入场动画类型
    "outro": OutroType  # 15. 视频/图片出场动画类型
}
descriptions = {
    "video_scene": "视频画面特效，包含各种视觉效果和场景特效",
    "tone": "音频音色特效，用于改变声音的音调和音色",
    "audio_scene": "音频场景特效，提供各种环境音效和声音处理",
    "filter": "滤镜特效，用于调整画面色彩和风格",
    "speech_to_song": "语音转歌曲特效",
    "mask": "蒙版特效",
    "transition": "转场特效",
    "font": "字体特效",
    "text_intro": "文字入场特效",
    "text_outro": "文字出场特效",
    "text_loop_anim": "文字循环动画特效",
    "group_animation": "组合动画特效",
    "video_character": "视频人物特效",
    "intro": "片头特效",
    "outro": "片尾特效"
}


@router.get("/findByType", response_model=EffectResponse)
async def get_all_effects(effect_type: Optional[str] = None, is_vip: Optional[bool] = None) -> EffectResponse:
    """
    获取所有特效的元数据

    该接口返回按特效类型分组的特效数据，支持按类型过滤。

    Args:
        effect_type: 可选的特效类型过滤器，支持: video_scene, audio_tone, audio_scene, filter

    Returns:
        EffectResponse: 包含按特效类型分组的特效数据字典

    Raises:
        HTTPException: 当特效类型参数无效时抛出HTTP异常
        :param effect_type:
        :param is_vip:
    """
    try:
        logger.info(f"开始获取特效数据，过滤类型: {effect_type}")
        # 如果指定了特效类型，只返回该类型的特效
        if effect_type:
            if effect_type not in EFFECT_TYPE_MAPPING:
                raise ValueError(f"无效的特效类型。支持的类型: {list(EFFECT_TYPE_MAPPING.keys())}")

            enum_class = EFFECT_TYPE_MAPPING[effect_type]
            result = _extract_effects_from_enum(enum_class, is_vip)
        else:
            """获取失败"""
            return EffectResponse(
                code=500,
                message="没有指定资源类型",
                data=None
            )

        return EffectResponse(
            code=200,
            message="特效数据获取成功",
            data=result
        )

    except ValueError as ve:
        logger.error(f"参数验证错误: {ve}")
        raise HTTPException(status_code=400, detail=str(ve))

    except Exception as e:
        logger.error(f"获取特效数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取特效数据失败: {str(e)}")


# ================== 辅助函数 ==================

def _extract_effects_from_enum(enum_class, is_vip: Optional[bool] = None) -> Optional[List[EffectData]]:
    """
    从枚举类中提取特效数据

    Args:
        enum_class: 特效枚举类

    Returns:
        List[EffectData]: 特效数据列表
    """
    result = []
    for effect_enum_member in enum_class:
        effect_meta = effect_enum_member.value
        effect_data = EffectData(
            resource_id=effect_meta.resource_id,
        )
        if hasattr(effect_meta, 'name'):
            effect_data.name = effect_meta.name
        if hasattr(effect_meta, 'title'):
            effect_data.name = effect_meta.title
        if hasattr(effect_meta, 'is_vip'):
            effect_data.is_vip = effect_meta.is_vip
        else:
            effect_data.is_vip = False
        if is_vip is not None and effect_data.is_vip != is_vip:
            continue
        result.append(effect_data)

    return result


@router.get("/all_types", response_model=EffectResponse)
def get_all_types() -> EffectResponse:
    """
    获取所有的资源类型列表
    :return:
    """
    return EffectResponse(
        code=200,
        message="获取成功",
        data=descriptions
    )