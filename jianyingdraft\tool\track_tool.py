# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> <PERSON><PERSON>reate Time: 2025/8/12 下午6:32
File Name: track_tool.py
轨道相关的MCP工具
"""
from typing import Optional
from mcp.server.fastmcp import FastMCP
from jianyingdraft.services.track_service import create_track_service
from jianyingdraft.utils.response import ToolResponse
from jianyingdraft.utils.index_manager import get_draft_id


def track_tools(mcp: FastMCP):
    @mcp.tool()
    def create_track(draft_id: str, track_type: str, track_name: str):
        """
        创建轨道

        Args:
            draft_id: 草稿ID
            track_type: 轨道类型，支持 "video", "audio", "text"
            track_name: 轨道名称,同类型轨道名不能相同

        Returns:
            dict: 包含操作结果的响应，格式为 {"success": bool, "message": str, "data": dict}

        Examples:
            创建视频轨道: create_track("draft_id", "video", "主视频")
            创建音频轨道: create_track("draft_id", "audio", "背景音乐")
            创建文本轨道: create_track("draft_id", "text", "字幕")
        """
        # 调用服务层处理业务逻辑
        result = create_track_service(draft_id, track_type, track_name)

        # 返回ToolResponse对象
        return result

    @mcp.tool()
    def create_track_by_any_id(any_id: str, track_type: str, track_name: Optional[str] = None) -> ToolResponse:
        """
        通过任意ID创建轨道（自动查找草稿ID）

        Args:
            any_id: 任意已存在的ID（草稿ID、轨道ID、片段ID等）
            track_type: 轨道类型，支持 "video", "audio", "text"
            track_name: 轨道名称（可选）。如果已存在同类型轨道，则必须提供名称以避免混淆

        Returns:
            ToolResponse: 包含操作结果的响应

        Examples:
            通过草稿ID创建: create_track_by_any_id("draft_id", "video", "主视频")
            通过轨道ID创建: create_track_by_any_id("track_id", "audio", "背景音乐")
            通过片段ID创建: create_track_by_any_id("video_segment_id", "text", "字幕")
        """
        # 通过任意ID查找草稿ID
        draft_id = get_draft_id(any_id)
        if not draft_id:
            return ToolResponse(
                success=False,
                message=f"无法找到ID '{any_id}' 对应的草稿"
            )

        # 调用原有的创建轨道服务
        return create_track_service(draft_id, track_type, track_name)
