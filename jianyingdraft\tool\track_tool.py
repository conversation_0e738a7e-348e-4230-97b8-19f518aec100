# -*- coding: utf-8 -*-
"""
Author: ji<PERSON> wei
Create Time: 2025/8/12 下午6:32
File Name: track_tool.py
轨道相关的MCP工具
"""
from typing import Optional
from mcp.server.fastmcp import FastMCP
from jianyingdraft.services.track_service import create_track_service
from jianyingdraft.utils.response import ToolResponse


def track_tools(mcp: FastMCP):
    @mcp.tool()
    def create_track(draft_id: str, track_type: str, track_name: Optional[str] = None):
        """
        创建轨道

        Args:
            draft_id: 草稿ID
            track_type: 轨道类型，支持 "video", "audio", "text"
            track_name: 轨道名称（可选）。如果已存在同类型轨道，则必须提供名称以避免混淆

        Returns:
            dict: 包含操作结果的响应，格式为 {"success": bool, "message": str, "data": dict}

        Examples:
            创建视频轨道: create_track("draft_id", "video", "主视频")
            创建音频轨道: create_track("draft_id", "audio", "背景音乐")
            创建文本轨道: create_track("draft_id", "text", "字幕")
        """
        # 调用服务层处理业务逻辑
        result = create_track_service(draft_id, track_type, track_name)

        # 返回字典格式的响应
        return result
