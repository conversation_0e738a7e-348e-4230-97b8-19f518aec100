# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> we<PERSON>
Create Time: 2025/8/12
File Name: video_service.py
视频服务层 - 封装视频片段相关的业务逻辑
"""
from typing import Optional, Dict, Any
from jianyingdraft.jianying.video import VideoSegment
from jianyingdraft.utils.response import ToolResponse


def add_video_segment_service(
    draft_id: str,
    material: str,
    target_timerange: str,
    source_timerange: Optional[str] = None,
    speed: Optional[float] = None,
    volume: float = 1.0,
    change_pitch: bool = False,
    clip_settings: Optional[Dict[str, Any]] = None,
    track_name: Optional[str] = None
) -> ToolResponse:
    """
    视频片段创建服务 - 封装复杂的视频片段创建逻辑
    
    Args:
        draft_id: 草稿ID
        material: 视频文件路径，包括文本文件路径或者url
        target_timerange: 片段在轨道上的目标时间范围，格式如 "0s-4.2s"
        source_timerange: 从源视频文件中截取的时间范围，格式如 "1s-4.2s"（可选）
        speed: 播放速度，默认为1.0（可选）
        volume: 音量，默认为1.0（可选）
        change_pitch: 是否跟随变速改变音调，默认为False（可选）
        clip_settings: 图像调节设置字典（可选）
        track_name: 指定的轨道名称（可选）
    
    Returns:
        ToolResponse: 包含操作结果的响应对象
    """
    try:
        # 创建VideoSegment实例
        video_segment = VideoSegment(draft_id, track_name=track_name)
        
        # 调用视频片段创建方法
        result_data = video_segment.add_video_segment(
            material=material,
            target_timerange=target_timerange,
            source_timerange=source_timerange,
            speed=speed,
            volume=volume,
            change_pitch=change_pitch,
            clip_settings=clip_settings,
            track_name=track_name
        )
        
        # 构建返回数据，包含video_segment_id
        response_data = {
            "video_segment_id": video_segment.video_segment_id,
            "draft_id": draft_id,
            "add_video_segment": result_data
        }
        
        # 如果有轨道名称，添加到返回数据中
        if track_name:
            response_data["track_name"] = track_name
        
        return ToolResponse(
            success=True,
            message="视频片段创建成功",
            data=response_data
        )
        
    except ValueError as e:
        # 处理参数错误（时间范围格式、轨道类型等）
        return ToolResponse(
            success=False,
            message=f"参数错误: {str(e)}"
        )
        
    except NameError as e:
        # 处理轨道不存在错误
        return ToolResponse(
            success=False,
            message=f"轨道错误: {str(e)}"
        )
        
    except TypeError as e:
        # 处理轨道类型错误
        return ToolResponse(
            success=False,
            message=f"轨道类型错误: {str(e)}"
        )
        
    except FileNotFoundError as e:
        # 处理文件不存在错误
        return ToolResponse(
            success=False,
            message=f"文件错误: {str(e)}"
        )
        
    except Exception as e:
        # 处理其他未预期的错误
        return ToolResponse(
            success=False,
            message=f"视频片段创建失败: {str(e)}"
        )


def add_video_animation_service(
    draft_id: str,
    video_segment_id: str,
    animation_type: str,
    animation_name: str,
    duration: Optional[str] = None
) -> ToolResponse:
    """
    视频动画添加服务 - 封装视频动画添加逻辑

    Args:
        draft_id: 草稿ID
        video_segment_id: 视频片段ID
        animation_type: 动画类型，支持 "IntroType", "OutroType", "GroupAnimationType"
        animation_name: 动画名称
        duration: 动画持续时间，如 "1s"（可选）

    Returns:
        ToolResponse: 包含操作结果的响应对象
    """
    try:
        # 创建VideoSegment实例
        video_segment = VideoSegment(draft_id, video_segment_id=video_segment_id)

        # 调用动画添加方法
        success = video_segment.add_animation(
            animation_type=animation_type,
            animation_name=animation_name,
            duration=duration,
            video_segment_id=video_segment_id
        )

        if success:
            # 构建返回数据
            response_data = {
                "draft_id": draft_id,
                "video_segment_id": video_segment_id,
                "animation_type": animation_type,
                "animation_name": animation_name
            }

            if duration:
                response_data["duration"] = duration

            return ToolResponse(
                success=True,
                message="视频动画添加成功",
                data=response_data
            )
        else:
            return ToolResponse(
                success=False,
                message="视频动画添加失败"
            )

    except ValueError as e:
        # 处理动画类型错误
        return ToolResponse(
            success=False,
            message=f"参数错误: {str(e)}"
        )

    except Exception as e:
        # 处理其他未预期的错误
        return ToolResponse(
            success=False,
            message=f"视频动画添加失败: {str(e)}"
        )
