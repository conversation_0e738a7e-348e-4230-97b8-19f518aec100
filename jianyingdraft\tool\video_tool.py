# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>reate Time: 2025/8/12
File Name: video_tool.py
视频相关的MCP工具
"""
from typing import Optional, Dict, Any
from mcp.server.fastmcp import FastMCP
from jianyingdraft.services.video_service import add_video_segment_service, add_video_animation_service, add_video_transition_service
from jianyingdraft.utils.response import ToolResponse


def video_tools(mcp: FastMCP):
    @mcp.tool()
    def add_video_segment(
            draft_id: str,
            material: str,
            target_timerange: str,
            source_timerange: Optional[str] = None,
            speed: Optional[float] = None,
            volume: float = 1.0,
            change_pitch: bool = False,
            clip_settings: Optional[Dict[str, Any]] = None,
            track_name: Optional[str] = None
    ) -> ToolResponse:
        """
        添加视频片段到指定轨道
        
        Args:
            draft_id: 草稿ID
            material: 视频文件路径，支持本地文件路径或URL
            target_timerange: 片段在轨道上的目标时间范围，格式如 "0s-4.2s"，表示在轨道上从0s开始，持续4.2s
            source_timerange: 从源视频文件中截取的时间范围，格式如 "1s-4.2s"，表示从源视频的1s开始截取，持续4.2s（可选）
            speed: 播放速度，默认为1.0。此项与source_timerange同时指定时，将覆盖target_timerange中的时长（可选）
            volume: 音量，默认为1.0（可选）
            change_pitch: 是否跟随变速改变音调，默认为False（可选）
            clip_settings: 图像调节设置字典（可选），包含以下字段：
                - alpha: 图像不透明度，0-1，默认为1.0
                - flip_horizontal: 是否水平翻转，默认为False
                - flip_vertical: 是否垂直翻转，默认为False
                - rotation: 顺时针旋转的角度，可正可负，默认为0.0
                - scale_x: 水平缩放比例，默认为1.0
                - scale_y: 垂直缩放比例，默认为1.0
                - transform_x: 水平位移，单位为半个画布宽，默认为0.0
                - transform_y: 垂直位移，单位为半个画布高，默认为0.0
            track_name: 指定的轨道名称（可选）。如果不指定，需要确保只有一个视频轨道
        
        Returns:
            ToolResponse: 包含操作结果的响应，格式为 {"success": bool, "message": str, "data": dict}
        
        Examples:
            # 基本用法
            add_video_segment("draft_id", "/path/to/video.mp4", "0s-5s", track_name="主视频")
            
            # 指定源时间范围
            add_video_segment("draft_id", "/path/to/video.mp4", "0s-3s", source_timerange="10s-3s", track_name="主视频")
            
            # 设置播放速度和音量
            add_video_segment("draft_id", "/path/to/video.mp4", "0s-5s", speed=2.0, volume=0.8, track_name="主视频")
            
            # 设置图像调节
            add_video_segment("draft_id", "/path/to/video.mp4", "0s-5s", 
                            clip_settings={"alpha": 0.8, "scale_x": 1.2, "rotation": 45}, 
                            track_name="主视频")
        """
        # 调用服务层处理业务逻辑
        result = add_video_segment_service(
            draft_id=draft_id,
            material=material,
            target_timerange=target_timerange,
            source_timerange=source_timerange,
            speed=speed,
            volume=volume,
            change_pitch=change_pitch,
            clip_settings=clip_settings,
            track_name=track_name
        )

        # 返回ToolResponse对象
        return result

    @mcp.tool()
    def add_video_animation(
        draft_id: str,
        video_segment_id: str,
        animation_type: str,
        animation_name: str,
        duration: Optional[str] = None
    ) -> ToolResponse:
        """
        为视频片段添加动画特效

        Args:
            draft_id: 草稿ID
            video_segment_id: 视频片段ID，必须是已存在的视频片段
            animation_type: 动画类型，支持以下类型：
                - "IntroType": 视频/图片入场动画类型
                - "OutroType": 视频/图片出场动画类型（出场动画不能与转场一起用）
                - "GroupAnimationType": 组合动画（该类型不能与其他两个同时存在）
            animation_name: 动画名称，具体的动画效果名称
            duration: 动画持续时间，格式如 "1s", "2.5s"（可选）

        Returns:
            ToolResponse: 包含操作结果的响应，格式为 {"success": bool, "message": str, "data": dict}

        Examples:
            # 添加入场动画
            add_video_animation("draft_id", "video_segment_id", "IntroType", "淡入", "1s")

            # 添加出场动画
            add_video_animation("draft_id", "video_segment_id", "OutroType", "淡出", "1.5s")

            # 添加组合动画
            add_video_animation("draft_id", "video_segment_id", "GroupAnimationType", "缩放")
        """
        # 调用服务层处理业务逻辑
        result = add_video_animation_service(
            draft_id=draft_id,
            video_segment_id=video_segment_id,
            animation_type=animation_type,
            animation_name=animation_name,
            duration=duration
        )

        # 返回ToolResponse对象
        return result

    @mcp.tool()
    def add_video_transition(
        draft_id: str,
        video_segment_id: str,
        transition_type: str,
        duration: Optional[str] = None
    ) -> ToolResponse:
        """
        为视频片段添加转场特效

        Args:
            draft_id: 草稿ID
            video_segment_id: 视频片段ID，必须是已存在的视频片段
            transition_type: 转场类型名称，如 "信号故障", "淡入淡出", "闪白", "黑场" 等
            duration: 转场持续时间，格式如 "1s", "0.5s"（可选）

        Returns:
            ToolResponse: 包含操作结果的响应，格式为 {"success": bool, "message": str, "data": dict}

        Examples:
            # 添加淡入淡出转场
            add_video_transition("draft_id", "video_segment_id", "淡入淡出", "1s")

            # 添加信号故障转场
            add_video_transition("draft_id", "video_segment_id", "信号故障", "0.5s")

            # 添加转场（使用默认时长）
            add_video_transition("draft_id", "video_segment_id", "闪白")
        """
        # 调用服务层处理业务逻辑
        result = add_video_transition_service(
            draft_id=draft_id,
            video_segment_id=video_segment_id,
            transition_type=transition_type,
            duration=duration
        )

        # 返回ToolResponse对象
        return result
