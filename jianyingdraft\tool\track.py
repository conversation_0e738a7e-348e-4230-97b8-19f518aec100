# -*- coding: utf-8 -*-
"""
Author: <PERSON><PERSON> <PERSON><PERSON>
Create Time: 2025/8/10 下午6:09
File Name:track.py
"""
import json
import os
import uuid
from datetime import datetime
from typing import Optional, Dict, Any
from jianyingdraft.config import SAVE_PATH
from jianyingdraft.utils.response import ToolResponse

class Track:
    """
    轨道管理类
    负责轨道的创建和数据存储
    """

    def __init__(self, draft_id: str, track_id: str = None):
        """
        初始化轨道

        Args:
            draft_id: 草稿ID
            track_id: 轨道ID
        """
        self.draft_id = draft_id
        self.track_id = track_id

    def add_track(self, track_type: str, track_name: Optional[str] = None) -> Dict[str, Any]:
        """
        添加轨道

        Args:
            track_type: 轨道类型，如 "video", "audio", "text"
            track_name: 轨道名称（可选）

        Returns:
            Dict[str, Any]: ToolResponse字典格式
        """
        try:
            # 1. 验证轨道类型
            self._validate_track_type(track_type)

            # 2. 验证轨道名称
            self._validate_track_name(track_name)

            # 3. 验证轨道唯一性
            self._validate_track_uniqueness(track_type, track_name)

            track_id = str(uuid.uuid4())

            # 构建轨道数据
            add_track_params = {
                "track_type": track_type
            }

            # 只添加用户明确传入的可选参数
            if track_name:
                add_track_params["track_name"] = track_name

            # 构建完整的轨道数据
            track_data = {
                "track_id": track_id,
                "operation": "add_track",
                "add_track": add_track_params,
                "created_at": datetime.now().isoformat()
            }

            # 保存参数
            self.add_json_to_file(track_data)
            self.track_id = track_id

            return ToolResponse(
                success=True,
                message="轨道添加成功",
                data={"track_id": track_id, "track_type": track_type, "track_name": track_name}
            ).__dict__

        except Exception as e:
            return ToolResponse(
                success=False,
                message=str(e),
                data={"error_type": type(e).__name__, "draft_id": self.draft_id,"track_type": track_type, "track_name": track_name}
            ).__dict__

    def add_json_to_file(self, new_data: Dict[str, Any]) -> bool:
        """
        向现有JSON文件中添加新的JSON数据，保持文件结构规范

        Args:
            new_data: 要添加的新数据

        Returns:
            bool: 添加是否成功
        """
        try:
            # 确保目录存在
            os.makedirs(f"{SAVE_PATH}/{self.draft_id}", exist_ok=True)
            file_path = f"{SAVE_PATH}/{self.draft_id}/track.json"

            # 读取现有数据
            existing_data = []
            if os.path.exists(file_path):
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        existing_data = json.load(f)
                        # 如果不是列表，转换为列表
                        if not isinstance(existing_data, list):
                            existing_data = [existing_data]
                except (json.JSONDecodeError, FileNotFoundError):
                    # 如果文件不存在或格式错误，初始化为空列表
                    existing_data = []

            # 添加新数据
            existing_data.append(new_data)

            # 保存为规范的JSON数组格式
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            print(f"添加JSON数据失败: {e}")
            return False

    def get_tracks(self) -> list:
        """
        获取所有轨道记录

        Returns:
            List: 轨道记录列表
        """
        try:
            file_path = f"{SAVE_PATH}/{self.draft_id}/track.json"
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return []
        except Exception as e:
            print(f"读取轨道数据失败: {e}")
            return []

    def get_track_by_id(self, track_id: str) -> Optional[Dict[str, Any]]:
        """
        根据轨道ID获取轨道信息

        Args:
            track_id: 轨道ID

        Returns:
            Dict: 轨道信息，如果不存在返回None
        """
        tracks = self.get_tracks()
        for track in tracks:
            if track.get("track_id") == track_id:
                return track
        return None

    def get_tracks_by_type(self, track_type: str) -> list:
        """
        根据轨道类型获取轨道列表

        Args:
            track_type: 轨道类型

        Returns:
            List: 指定类型的轨道列表
        """
        tracks = self.get_tracks()
        result = []
        for track in tracks:
            add_track_data = track.get("add_track", {})
            if add_track_data.get("track_type") == track_type:
                result.append(track)
        return result

    def delete_track(self, track_id: str) -> bool:
        """
        删除指定轨道

        Args:
            track_id: 轨道ID

        Returns:
            bool: 删除是否成功
        """
        try:
            tracks = self.get_tracks()
            updated_tracks = [track for track in tracks if track.get("track_id") != track_id]

            if len(updated_tracks) == len(tracks):
                print(f"未找到轨道ID: {track_id}")
                return False

            file_path = f"{SAVE_PATH}/{self.draft_id}/track.json"
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(updated_tracks, f, ensure_ascii=False, indent=2)

            return True

        except Exception as e:
            print(f"删除轨道失败: {e}")
            return False

    def get_track_count(self) -> int:
        """
        获取轨道总数

        Returns:
            int: 轨道数量
        """
        return len(self.get_tracks())

    def get_track_count_by_type(self, track_type: str) -> int:
        """
        获取指定类型的轨道数量

        Args:
            track_type: 轨道类型

        Returns:
            int: 指定类型的轨道数量
        """
        return len(self.get_tracks_by_type(track_type))

    def _validate_track_type(self, track_type: str):
        """验证轨道类型"""
        valid_types = {"video", "audio", "text"}
        if track_type not in valid_types:
            raise ValueError(f"无效的轨道类型: {track_type}，支持的类型: {', '.join(valid_types)}")

    def _validate_track_name(self, track_name: str):
        """验证轨道名称格式"""
        if not track_name or not track_name.strip():
            raise ValueError("轨道名称不能为空")

        if len(track_name) > 50:
            raise ValueError("轨道名称长度不能超过50个字符")

        forbidden_chars = ["<", ">", ":", "\"", "|", "?", "*"]
        for char in forbidden_chars:
            if char in track_name:
                raise ValueError(f"轨道名称不能包含字符: {char}")

    def _validate_track_uniqueness(self, track_type: str, track_name: Optional[str]):
        """验证轨道唯一性"""
        existing_tracks = self.get_tracks()

        # 检查轨道名称唯一性
        if track_name:
            for track in existing_tracks:
                add_track_data = track.get("add_track", {})
                if add_track_data.get("track_name") == track_name:
                    raise NameError(f"轨道名称已存在: {track_name}")

        # 检查同类型轨道是否需要命名
        same_type_tracks = self.get_tracks_by_type(track_type)
        if len(same_type_tracks) > 0 and not track_name:
            raise NameError(f"已存在 {track_type} 类型的轨道，请为新轨道指定名称以避免混淆")

    def validate_track_exists(self, track_name: str) -> bool:
        """验证轨道是否存在"""
        if not track_name:
            return False

        existing_tracks = self.get_tracks()
        for track in existing_tracks:
            add_track_data = track.get("add_track", {})
            if add_track_data.get("track_name") == track_name:
                return True
        return False

    def get_track_by_name(self, track_name: str) -> Optional[Dict[str, Any]]:
        """根据轨道名称获取轨道信息"""
        existing_tracks = self.get_tracks()
        for track in existing_tracks:
            add_track_data = track.get("add_track", {})
            if add_track_data.get("track_name") == track_name:
                return track
        return None


if __name__ == "__main__":
    # 测试轨道类型和名称验证
    print("=" * 50)
    print("测试轨道类型和名称验证")
    print("=" * 50)

    # 创建测试草稿ID
    test_draft_id = "873b65fc-d7f9-4014-8485-f970e587a4af"
    track = Track(test_draft_id)

    # 测试1: 正常添加轨道
    print("\n1. 测试正常添加轨道:")
    try:
        track_id1 = track.add_track("video", "主视频轨道")
        print(f"✅ 成功添加视频轨道: {track_id1}")
    except Exception as e:
        print(f"❌ 添加失败: {e}")

    # 测试2: 无效轨道类型
    print("\n2. 测试无效轨道类型:")
    try:
        track.add_track("invalid_type", "测试轨道")
        print("❌ 不应该成功")
    except ValueError as e:
        print(f"✅ 正确捕获类型错误: {e}")

    # 测试3: 重复轨道名称
    print("\n3. 测试重复轨道名称:")
    try:
        track.add_track("audio", "主视频轨道")  # 与已存在的名称重复
        print("❌ 不应该成功")
    except NameError as e:
        print(f"✅ 正确捕获名称重复错误: {e}")

    # 测试4: 同类型轨道未命名
    print("\n4. 测试同类型轨道未命名:")
    try:
        track.add_track("video")  # 已存在video轨道，必须命名
        print("❌ 不应该成功")
    except NameError as e:
        print(f"✅ 正确捕获未命名错误: {e}")

    # 测试5: 轨道名称格式验证
    print("\n5. 测试轨道名称格式验证:")
    try:
        track.add_track("audio", "包含<>的名称")  # 包含禁用字符
        print("❌ 不应该成功")
    except ValueError as e:
        print(f"✅ 正确捕获格式错误: {e}")

    # 测试6: 空轨道名称
    print("\n6. 测试空轨道名称:")
    try:
        track.add_track("text", "")  # 空名称
        print("❌ 不应该成功")
    except ValueError as e:
        print(f"✅ 正确捕获空名称错误: {e}")

    # 测试7: 正常添加多个不同类型轨道
    print("\n7. 测试添加多个不同类型轨道:")
    try:
        track_id2 = track.add_track("audio", "背景音乐")
        track_id3 = track.add_track("text", "字幕轨道")
        print(f"✅ 成功添加音频轨道: {track_id2}")
        print(f"✅ 成功添加文本轨道: {track_id3}")
    except Exception as e:
        print(f"❌ 添加失败: {e}")

    # 测试8: 轨道查询功能
    print("\n8. 测试轨道查询功能:")
    try:
        all_tracks = track.get_tracks()
        print(f"✅ 总轨道数: {len(all_tracks)}")

        video_tracks = track.get_tracks_by_type("video")
        print(f"✅ 视频轨道数: {len(video_tracks)}")

        track_exists = track.validate_track_exists("主视频轨道")
        print(f"✅ 轨道存在验证: {track_exists}")

        track_info = track.get_track_by_name("背景音乐")
        print(f"✅ 按名称查找轨道: {'找到' if track_info else '未找到'}")

    except Exception as e:
        print(f"❌ 查询失败: {e}")

    print("\n" + "=" * 50)
    print("✅ 轨道验证测试完成")
    print("=" * 50)
