import inspect
from typing import Any, Dict, Type, TypeVar, get_origin,Optional

# 定义一个 TypeVar 来表示任何类
T = TypeVar('T')

def convert_dict_to_object(data: Dict[str, Any], target_class: Type[T]) -> T:
    """
    一个通用的函数，将字典转换为指定类的实例。
    它会尝试将字典的键名（支持camelCase）映射到类构造函数的snake_case参数名。

    Args:
        data (Dict[str, Any]): 要转换的输入字典。
        target_class (Type[T]): 目标类的类型。

    Returns:
        T: target_class 的一个实例，属性由字典数据填充。
    """
    if not data:
        return None
    if not isinstance(data, dict):
        raise TypeError(f"Input data must be a dictionary, got {type(data).__name__}")

    # 获取目标类构造函数的所有参数
    # signature 返回一个 Signature 对象，描述了函数的参数
    sig = inspect.signature(target_class.__init__)

    # 准备传递给构造函数的参数
    init_params = {}

    for param_name, param in sig.parameters.items():
        # 跳过 self 参数
        if param_name == 'self':
            continue

        # 尝试从字典中获取值，优先匹配完全一致的参数名
        # 其次尝试将参数名从 snake_case 转换为 camelCase 在字典中查找
        # 再次尝试将参数名从 snake_case 转换为 PascalCase (首字母大写) 在字典中查找
        # 实际情况中，你可能只需要 camelCase -> snake_case 的转换

        # 1. 直接匹配 (如 alpha -> alpha)
        if param_name in data:
            value = data[param_name]
        else:
            # 2. 尝试从 camelCase 键名匹配 (如 flip_horizontal -> flipHorizontal)
            # 将 snake_case 转换为 camelCase 的辅助函数
            camel_case_param_name = ''.join(word.capitalize() if i > 0 else word for i, word in enumerate(param_name.split('_')))
            if camel_case_param_name in data:
                value = data[camel_case_param_name]
            else:
                # 3. 如果仍未找到，检查参数是否有默认值
                if param.default is not inspect.Parameter.empty:
                    # 使用构造函数定义的默认值
                    value = param.default
                else:
                    # 如果是可选参数 (Optional[...]) 且没有默认值，将其视为 None
                    # 检查类型提示是否为 Optional
                    if get_origin(param.annotation) is Optional:
                        value = None
                    else:
                        # 如果是必填参数且字典中没有对应的值，则抛出错误
                        raise ValueError(
                            f"Missing required parameter '{param_name}' for {target_class.__name__} "
                            f"from input dictionary. (Tried '{param_name}' and '{camel_case_param_name}')"
                        )

        # 递归处理嵌套字典，如果参数类型本身不是基本类型，且对应的值是字典
        # 这一步比较复杂，需要知道 param.annotation 对应的类型是否也需要转换
        # 为了通用性，这里简化处理，假设参数类型就是字典中的类型，不进行深层自动转换。
        # 如果需要深层嵌套转换，需要额外逻辑判断 param.annotation 是不是另一个 class，然后递归调用。
        # 对于 ClipSettings 这样的扁平结构，不需要这一步。
        # 如果你的类参数本身就是另一个自定义类，比如 `address: Address`，那么需要在这里递归调用 `convert_dict_to_object`
        # 例如：
        # if inspect.isclass(param.annotation) and isinstance(value, dict):
        #     value = convert_dict_to_object(value, param.annotation)

        init_params[param_name] = value

    # 使用收集到的参数创建类的实例
    return target_class(**init_params)